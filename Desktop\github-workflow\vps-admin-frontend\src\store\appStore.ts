/**
 * Global application state management using Zustand
 * Provides centralized state for the VPS Admin application
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { Message, Task, ExecutionStats, SSHInfo } from '../types';

interface AppState {
  // UI State
  isWaiting: boolean;
  showConfirmation: boolean;
  commandToConfirm: string | null;
  error: string | null;
  isAwaitingAnswer: boolean;
  showStatsPanel: boolean;
  autoScroll: boolean;
  theme: 'light' | 'dark';

  // Task Management
  taskId: string | null;
  isTaskActive: boolean;
  currentTask: Task | null;
  taskHistory: Task[];

  // Messages
  messages: Message[];

  // Statistics
  executionStats: ExecutionStats;

  // SSH Info
  sshInfo: SSHInfo | null;

  // Command History
  commandHistory: string[];
  historyIndex: number;

  // Performance Metrics
  performanceMetrics: {
    renderTime: number;
    apiResponseTime: number;
    memoryUsage: number;
  };
}

interface AppActions {
  // UI Actions
  setIsWaiting: (waiting: boolean) => void;
  setShowConfirmation: (show: boolean) => void;
  setCommandToConfirm: (command: string | null) => void;
  setError: (error: string | null) => void;
  setIsAwaitingAnswer: (awaiting: boolean) => void;
  setShowStatsPanel: (show: boolean) => void;
  setAutoScroll: (autoScroll: boolean) => void;
  setTheme: (theme: 'light' | 'dark') => void;

  // Task Actions
  setTaskId: (id: string | null) => void;
  setIsTaskActive: (active: boolean) => void;
  setCurrentTask: (task: Task | null) => void;
  addTaskToHistory: (task: Task) => void;
  updateTaskInHistory: (taskId: string, updates: Partial<Task>) => void;
  clearTaskHistory: () => void;

  // Message Actions
  addMessage: (message: Message) => void;
  setMessages: (messages: Message[]) => void;
  clearMessages: () => void;

  // Statistics Actions
  updateExecutionStats: (stats: Partial<ExecutionStats>) => void;
  incrementCommandCount: (success: boolean) => void;
  resetStats: () => void;

  // SSH Actions
  setSSHInfo: (info: SSHInfo | null) => void;

  // Command History Actions
  addToCommandHistory: (command: string) => void;
  setHistoryIndex: (index: number) => void;
  clearCommandHistory: () => void;

  // Performance Actions
  updatePerformanceMetrics: (metrics: Partial<AppState['performanceMetrics']>) => void;

  // Utility Actions
  resetApp: () => void;
}

const initialState: AppState = {
  // UI State
  isWaiting: false,
  showConfirmation: false,
  commandToConfirm: null,
  error: null,
  isAwaitingAnswer: false,
  showStatsPanel: false,
  autoScroll: true,
  theme: 'light',

  // Task Management
  taskId: null,
  isTaskActive: false,
  currentTask: null,
  taskHistory: [],

  // Messages
  messages: [],

  // Statistics
  executionStats: {
    totalCommands: 0,
    successfulCommands: 0,
    failedCommands: 0,
    averageExecutionTime: 0,
    uptime: 0,
    lastCommandTime: null,
  },

  // SSH Info
  sshInfo: null,

  // Command History
  commandHistory: [],
  historyIndex: -1,

  // Performance Metrics
  performanceMetrics: {
    renderTime: 0,
    apiResponseTime: 0,
    memoryUsage: 0,
  },
};

export const useAppStore = create<AppState & AppActions>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // UI Actions
        setIsWaiting: (waiting) => set({ isWaiting: waiting }),
        setShowConfirmation: (show) => set({ showConfirmation: show }),
        setCommandToConfirm: (command) => set({ commandToConfirm: command }),
        setError: (error) => set({ error }),
        setIsAwaitingAnswer: (awaiting) => set({ isAwaitingAnswer: awaiting }),
        setShowStatsPanel: (show) => set({ showStatsPanel: show }),
        setAutoScroll: (autoScroll) => set({ autoScroll }),
        setTheme: (theme) => set({ theme }),

        // Task Actions
        setTaskId: (id) => set({ taskId: id }),
        setIsTaskActive: (active) => set({ isTaskActive: active }),
        setCurrentTask: (task) => set({ currentTask: task }),
        addTaskToHistory: (task) => set((state) => ({
          taskHistory: [task, ...state.taskHistory.slice(0, 49)] // Keep last 50 tasks
        })),
        updateTaskInHistory: (taskId, updates) => set((state) => ({
          taskHistory: state.taskHistory.map(task =>
            task.id === taskId ? { ...task, ...updates } : task
          )
        })),
        clearTaskHistory: () => set({ taskHistory: [] }),

        // Message Actions
        addMessage: (message) => set((state) => ({
          messages: [...state.messages, message]
        })),
        setMessages: (messages) => set({ messages }),
        clearMessages: () => set({ messages: [] }),

        // Statistics Actions
        updateExecutionStats: (stats) => set((state) => ({
          executionStats: { ...state.executionStats, ...stats }
        })),
        incrementCommandCount: (success) => set((state) => ({
          executionStats: {
            ...state.executionStats,
            totalCommands: state.executionStats.totalCommands + 1,
            successfulCommands: success
              ? state.executionStats.successfulCommands + 1
              : state.executionStats.successfulCommands,
            failedCommands: !success
              ? state.executionStats.failedCommands + 1
              : state.executionStats.failedCommands,
            lastCommandTime: new Date(),
          }
        })),
        resetStats: () => set({
          executionStats: initialState.executionStats
        }),

        // SSH Actions
        setSSHInfo: (info) => set({ sshInfo: info }),

        // Command History Actions
        addToCommandHistory: (command) => set((state) => ({
          commandHistory: [command, ...state.commandHistory.slice(0, 49)], // Keep last 50 commands
          historyIndex: -1
        })),
        setHistoryIndex: (index) => set({ historyIndex: index }),
        clearCommandHistory: () => set({ commandHistory: [], historyIndex: -1 }),

        // Performance Actions
        updatePerformanceMetrics: (metrics) => set((state) => ({
          performanceMetrics: { ...state.performanceMetrics, ...metrics }
        })),

        // Utility Actions
        resetApp: () => set(initialState),
      }),
      {
        name: 'vps-admin-storage',
        partialize: (state) => ({
          theme: state.theme,
          autoScroll: state.autoScroll,
          taskHistory: state.taskHistory,
          commandHistory: state.commandHistory,
          executionStats: state.executionStats,
        }),
      }
    ),
    {
      name: 'vps-admin-store',
    }
  )
);

// Selectors for optimized component subscriptions
export const useUIState = () => useAppStore((state) => ({
  isWaiting: state.isWaiting,
  showConfirmation: state.showConfirmation,
  commandToConfirm: state.commandToConfirm,
  error: state.error,
  isAwaitingAnswer: state.isAwaitingAnswer,
  showStatsPanel: state.showStatsPanel,
  autoScroll: state.autoScroll,
  theme: state.theme,
}));

export const useTaskState = () => useAppStore((state) => ({
  taskId: state.taskId,
  isTaskActive: state.isTaskActive,
  currentTask: state.currentTask,
  taskHistory: state.taskHistory,
}));

export const useMessageState = () => useAppStore((state) => ({
  messages: state.messages,
}));

export const useStatsState = () => useAppStore((state) => ({
  executionStats: state.executionStats,
  performanceMetrics: state.performanceMetrics,
}));
