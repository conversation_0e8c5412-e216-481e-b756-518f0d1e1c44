<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>VPS AI ADMIN</title>

    <!-- Theme loading script - prevents flash of light theme -->
    <script>
      (function() {
        try {
          // Get theme from localStorage synchronously
          const stored = localStorage.getItem('vps-admin-storage');
          if (stored) {
            const parsed = JSON.parse(stored);
            const theme = parsed.state?.theme || 'light';

            // Apply theme class immediately
            if (theme === 'dark') {
              document.documentElement.classList.add('dark');
            } else {
              document.documentElement.classList.remove('dark');
            }
          }
        } catch (e) {
          // If anything fails, default to light theme (no class needed)
          console.warn('Failed to load theme from localStorage:', e);
        }
      })();
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
