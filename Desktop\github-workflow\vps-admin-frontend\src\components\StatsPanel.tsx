/**
 * StatsPanel component for displaying statistics and quick actions
 */

import React from 'react';
import { Target, XCircle, Download, Play, Pause } from 'lucide-react';
import { StatsPanelProps } from '../types';

const StatsPanel: React.FC<StatsPanelProps> = ({
  executionStats,
  currentTask,
  taskHistory,
  messages,
  autoScroll,
  onClose,
  onExportHistory,
  onToggleAutoScroll
}) => {
  const getSuccessRate = () => {
    if (executionStats.totalCommands === 0) return 0;
    return Math.round((executionStats.successfulCommands / executionStats.totalCommands) * 100);
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours}h ${minutes}m ${secs}s`;
  };

  return (
    <div className="bg-theme-primary border-0 lg:border border-theme-primary rounded-none lg:rounded-xl shadow-none lg:shadow-2xl h-full w-full p-3 lg:p-4 overflow-y-auto transition-all duration-300">
      <div className="flex items-center justify-between mb-3 lg:mb-4">
        <h3 className="font-semibold text-theme-primary flex items-center gap-2">
          <Target size={16} className="text-accent-primary" />
          Statistics
        </h3>
        <button
          onClick={onClose}
          className="text-theme-tertiary hover:text-theme-secondary dark:hover:text-theme-primary transition-colors p-1 touch-manipulation rounded-lg hover:bg-surface-secondary"
          title="Close Statistics Panel"
        >
          <XCircle size={16} />
        </button>
      </div>

      <div className="space-y-4">
        {/* Execution Stats */}
        <div className="bg-surface-primary p-2 lg:p-3 rounded-xl border border-theme-primary">
          <h4 className="font-medium text-sm text-theme-secondary mb-2">Command Execution</h4>
          <div className="grid grid-cols-2 gap-1.5 lg:gap-2 text-xs">
            <div className="bg-surface-secondary p-2 rounded-lg border border-theme-primary">
              <div className="text-theme-tertiary">Total</div>
              <div className="font-bold text-base lg:text-lg text-theme-primary">{executionStats.totalCommands}</div>
            </div>
            <div className="bg-surface-secondary p-2 rounded-lg border border-theme-primary">
              <div className="text-theme-tertiary">Success Rate</div>
              <div className="font-bold text-base lg:text-lg text-accent-secondary">
                {getSuccessRate()}%
              </div>
            </div>
            <div className="bg-surface-secondary p-2 rounded-lg border border-theme-primary">
              <div className="text-theme-tertiary">Successful</div>
              <div className="font-bold text-base lg:text-lg text-accent-secondary">{executionStats.successfulCommands}</div>
            </div>
            <div className="bg-surface-secondary p-2 rounded-lg border border-theme-primary">
              <div className="text-theme-tertiary">Failed</div>
              <div className="font-bold text-lg text-accent-error">{executionStats.failedCommands}</div>
            </div>
          </div>
          {executionStats.averageExecutionTime > 0 && (
            <div className="mt-2 text-xs text-theme-tertiary">
              Avg. execution time: {executionStats.averageExecutionTime}ms
            </div>
          )}
        </div>

        {/* Current Task */}
        {currentTask && (
          <div className="bg-surface-primary p-3 rounded-xl border border-theme-primary">
            <h4 className="font-medium text-sm text-theme-secondary mb-2">Current Task</h4>
            <div className="text-xs">
              <div className="font-medium truncate text-theme-primary">{currentTask.title}</div>
              <div className="text-theme-tertiary mt-1">
                Status: <span className={`font-medium ${
                  currentTask.status === 'running' ? 'text-accent-primary' :
                  currentTask.status === 'completed' ? 'text-accent-secondary' :
                  currentTask.status === 'failed' ? 'text-accent-error' :
                  'text-theme-secondary'
                }`}>
                  {currentTask.status}
                </span>
              </div>
              <div className="text-theme-tertiary mt-1">Progress: {Math.round(currentTask.progress)}%</div>
              <div className="w-full bg-surface-tertiary rounded-full h-2 mt-1 border border-theme-primary">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    currentTask.status === 'completed' ? 'bg-accent-secondary' :
                    currentTask.status === 'failed' ? 'bg-accent-error' :
                    'bg-accent-primary'
                  }`}
                  style={{ width: `${currentTask.progress}%` }}
                />
              </div>
              {currentTask.startTime && (
                <div className="text-theme-tertiary mt-1">
                  Started: {currentTask.startTime.toLocaleTimeString()}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Task History Summary */}
        {taskHistory.length > 0 && (
          <div className="bg-surface-primary p-3 rounded-xl border border-theme-primary">
            <h4 className="font-medium text-sm text-theme-secondary mb-2">Task History</h4>
            <div className="text-xs">
              <div className="flex justify-between text-theme-tertiary">
                <span>Total tasks:</span>
                <span className="font-mono text-theme-primary">{taskHistory.length}</span>
              </div>
              <div className="flex justify-between mt-1 text-theme-tertiary">
                <span>Completed:</span>
                <span className="font-mono text-accent-secondary">
                  {taskHistory.filter(t => t.status === 'completed').length}
                </span>
              </div>
              <div className="flex justify-between mt-1 text-theme-tertiary">
                <span>Failed:</span>
                <span className="font-mono text-accent-error">
                  {taskHistory.filter(t => t.status === 'failed').length}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Session Info */}
        <div className="bg-surface-primary p-3 rounded-xl border border-theme-primary">
          <h4 className="font-medium text-sm text-theme-secondary mb-2">Session Info</h4>
          <div className="text-xs">
            <div className="flex justify-between text-theme-tertiary">
              <span>Uptime:</span>
              <span className="font-mono text-theme-primary">{formatUptime(executionStats.uptime)}</span>
            </div>
            <div className="flex justify-between mt-1 text-theme-tertiary">
              <span>Messages:</span>
              <span className="font-mono text-theme-primary">{messages.length}</span>
            </div>
            <div className="flex justify-between mt-1 text-theme-tertiary">
              <span>Auto-scroll:</span>
              <span className={`font-mono ${autoScroll ? 'text-accent-secondary' : 'text-theme-quaternary'}`}>
                {autoScroll ? 'On' : 'Off'}
              </span>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-surface-primary p-3 rounded-xl border border-theme-primary">
          <h4 className="font-medium text-sm text-theme-secondary mb-2">Quick Actions</h4>
          <div className="space-y-2">
            <button
              onClick={onExportHistory}
              className="w-full text-xs bg-accent-primary text-white px-3 py-2 rounded-lg hover:opacity-90 flex items-center gap-2 transition-all duration-200 hover:transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              disabled={taskHistory.length === 0 && messages.length === 0}
            >
              <Download size={12} />
              Export History
            </button>
            <button
              onClick={onToggleAutoScroll}
              className={`w-full text-xs px-3 py-2 rounded-lg flex items-center gap-2 transition-all duration-200 hover:transform hover:scale-105 ${
                autoScroll
                  ? 'bg-accent-secondary text-white hover:opacity-90'
                  : 'bg-surface-tertiary text-theme-secondary hover:bg-interactive-hover border border-theme-secondary'
              }`}
            >
              {autoScroll ? <Pause size={12} /> : <Play size={12} />}
              Auto Scroll: {autoScroll ? 'On' : 'Off'}
            </button>
          </div>
        </div>

        {/* Performance Metrics */}
        {executionStats.totalCommands > 0 && (
          <div className="bg-surface-primary p-3 rounded-xl border border-theme-primary transition-all duration-300">
            <h4 className="font-medium text-sm text-theme-secondary mb-2">Performance</h4>
            <div className="text-xs space-y-1">
              <div className="flex justify-between text-theme-tertiary">
                <span>Commands/min:</span>
                <span className="font-mono text-theme-primary">
                  {executionStats.uptime > 0
                    ? Math.round((executionStats.totalCommands / executionStats.uptime) * 60)
                    : 0
                  }
                </span>
              </div>
              <div className="flex justify-between text-theme-tertiary">
                <span>Error rate:</span>
                <span className="font-mono text-accent-error">
                  {Math.round((executionStats.failedCommands / executionStats.totalCommands) * 100)}%
                </span>
              </div>
            </div>
          </div>
        )}

        {/* System Status */}
        <div className="bg-surface-primary p-3 rounded-xl border border-theme-primary transition-all duration-300">
          <h4 className="font-medium text-sm text-theme-secondary mb-2">System Status</h4>
          <div className="text-xs space-y-1">
            <div className="flex items-center justify-between text-theme-tertiary">
              <span>Connection:</span>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-accent-secondary rounded-full animate-pulse"></div>
                <span className="text-accent-secondary font-medium">Active</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatsPanel;
